/**
 * WCAG-065: Help Check (3.3.5 Level AAA)
 * 80% Automated - Detects availability of contextual help
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface HelpConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableContentQualityAnalysis?: boolean;
}

export class HelpCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private contentQualityAnalyzer = ContentQualityAnalyzer.getInstance();

  async performCheck(config: HelpConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: HelpConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 2000,
      },
      enableFormAccessibilityAnalysis: true,
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableContentQualityAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-065',
      'Help',
      'robust',
      0.0305,
      'AAA',
      enhancedConfig,
      this.executeHelpCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with help availability analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-065',
        ruleName: 'Help',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'help-availability-analysis',
          contextualHelpDetection: true,
          helpAccessibilityValidation: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 25,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeHelpCheck(
    page: Page,
    _config: HelpConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced form accessibility analysis using FormAccessibilityAnalyzer
    const formAccessibilityReport = await this.formAccessibilityAnalyzer.analyzeFormAccessibility(
      page,
      {
        analyzeLabels: true,
        analyzeValidation: true,
        analyzeKeyboardAccess: true,
        strictMode: true,
      },
    );

    // Analyze help content using enhanced analyzer
    const helpContentAnalysis = await this.analyzeHelpContentEnhanced(page, formAccessibilityReport);

    // Analyze context-sensitive help validation
    const contextSensitiveHelpAnalysis = await this.analyzeContextSensitiveHelp(page);

    // Analyze assistance mechanism testing
    const assistanceMechanismAnalysis = await this.analyzeAssistanceMechanisms(page);

    // Combine analysis results
    const allAnalyses = [helpContentAnalysis, contextSensitiveHelpAnalysis, assistanceMechanismAnalysis];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze help content using FormAccessibilityAnalyzer
   */
  private async analyzeHelpContentEnhanced(page: Page, formAccessibilityReport: any) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    let totalChecks = 0;
    let passedChecks = 0;

    formAccessibilityReport.forms.forEach((form: any, formIndex: number) => {
      totalChecks++;

      // Check if complex forms have help available
      const isComplexForm = form.fields.length > 5 ||
                           form.fields.some((field: any) => field.hasValidation || field.isRequired);

      if (isComplexForm) {
        // Check for form-level help
        const hasFormHelp = form.hasHelp || form.hasInstructions;

        if (hasFormHelp) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Complex form ${formIndex + 1} has help available`,
            value: `${form.selector} - form has help or instructions`,
            selector: form.selector,
            severity: 'info',
          });
        } else {
          issues.push(`Complex form ${formIndex + 1} lacks help content`);
          evidence.push({
            type: 'code',
            description: `Complex form ${formIndex + 1} requires help content`,
            value: `${form.selector} - complex form without help (${form.fields.length} fields)`,
            selector: form.selector,
            severity: 'warning',
          });
          recommendations.push(`Add help content to complex form ${formIndex + 1}`);
        }
      } else {
        // Simple forms pass by default
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Simple form ${formIndex + 1} does not require help`,
          value: `${form.selector} - simple form (${form.fields.length} fields)`,
          selector: form.selector,
          severity: 'info',
        });
      }

      // Check individual field help for complex fields
      form.fields.forEach((field: any, fieldIndex: number) => {
        if (field.hasValidation || field.isRequired) {
          totalChecks++;

          if (field.hasHelp || field.hasInstructions) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Complex field ${fieldIndex + 1} has help available`,
              value: `${field.selector} - field has help or instructions`,
              selector: field.selector,
              severity: 'info',
            });
          } else {
            issues.push(`Complex field ${fieldIndex + 1} lacks help content`);
            evidence.push({
              type: 'code',
              description: `Complex field ${fieldIndex + 1} needs help content`,
              value: `${field.selector} - required/validated field without help`,
              selector: field.selector,
              severity: 'warning',
            });
            recommendations.push(`Add help content to complex field ${fieldIndex + 1}`);
          }
        }
      });
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze context-sensitive help validation
   */
  private async analyzeContextSensitiveHelp(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for context-sensitive help elements
    const contextHelpElements = await page.$$eval(
      '.help, .help-text, .tooltip, .popover, [aria-describedby], [title], .help-icon, .help-button',
      (elements) => {
        return elements.map((element, index) => {
          const isVisible = element.offsetParent !== null;
          const hasKeyboardAccess = element.tabIndex >= 0 ||
                                   ['BUTTON', 'A', 'INPUT'].includes(element.tagName);
          const hasAriaLabel = element.hasAttribute('aria-label') ||
                              element.hasAttribute('aria-labelledby');
          const helpText = element.textContent?.trim() ||
                          element.getAttribute('title') ||
                          element.getAttribute('aria-label') || '';

          const associatedField = element.getAttribute('aria-describedby') ||
                                 element.closest('form')?.querySelector(`[aria-describedby="${element.id}"]`);

          return {
            index,
            selector: `help-element-${index}`,
            isVisible,
            hasKeyboardAccess,
            hasAriaLabel,
            helpText,
            hasAssociatedField: !!associatedField,
            isContextual: !!associatedField || element.closest('.form-group, .field, .input-group'),
          };
        });
      },
    );

    const totalChecks = contextHelpElements.length;
    let passedChecks = 0;

    contextHelpElements.forEach((helpElement, index) => {
      let helpPassed = true;

      // Check if help is accessible
      if (!helpElement.hasKeyboardAccess) {
        helpPassed = false;
        issues.push(`Help element ${index + 1} lacks keyboard access`);
        evidence.push({
          type: 'code',
          description: `Help element ${index + 1} needs keyboard accessibility`,
          value: `hasKeyboardAccess: ${helpElement.hasKeyboardAccess}`,
          severity: 'error',
        });
        recommendations.push(`Make help element ${index + 1} keyboard accessible`);
      }

      // Check if help has meaningful content
      if (helpElement.helpText.length < 10) {
        helpPassed = false;
        issues.push(`Help element ${index + 1} has insufficient content`);
        evidence.push({
          type: 'code',
          description: `Help element ${index + 1} needs more detailed content`,
          value: `helpText: "${helpElement.helpText}" (${helpElement.helpText.length} chars)`,
          severity: 'warning',
        });
        recommendations.push(`Add more detailed help content to element ${index + 1}`);
      }

      // Check if help is contextual
      if (!helpElement.isContextual) {
        issues.push(`Help element ${index + 1} is not contextual`);
        evidence.push({
          type: 'code',
          description: `Help element ${index + 1} should be contextual`,
          value: `isContextual: ${helpElement.isContextual}`,
          severity: 'warning',
        });
        recommendations.push(`Make help element ${index + 1} contextual to specific form fields`);
      }

      if (helpPassed) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Help element ${index + 1} is accessible and contextual`,
          value: `"${helpElement.helpText}" - accessible and contextual`,
          severity: 'info',
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze assistance mechanism testing
   */
  private async analyzeAssistanceMechanisms(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for various assistance mechanisms
    const assistanceMechanisms = await page.$$eval('*', (elements) => {
      const mechanisms = {
        helpLinks: 0,
        tooltips: 0,
        helpButtons: 0,
        instructions: 0,
        examples: 0,
        glossaries: 0,
        tutorials: 0,
      };

      elements.forEach((element) => {
        const text = element.textContent?.toLowerCase() || '';
        const href = element.getAttribute('href')?.toLowerCase() || '';
        const className = element.className?.toLowerCase() || '';
        const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';

        // Count different types of assistance mechanisms
        if (href.includes('help') || text.includes('help') || ariaLabel.includes('help')) {
          mechanisms.helpLinks++;
        }
        if (className.includes('tooltip') || element.hasAttribute('title')) {
          mechanisms.tooltips++;
        }
        if (className.includes('help') && element.tagName === 'BUTTON') {
          mechanisms.helpButtons++;
        }
        if (text.includes('instruction') || text.includes('how to') || text.includes('example')) {
          mechanisms.instructions++;
        }
        if (text.includes('example:') || text.includes('e.g.') || text.includes('for example')) {
          mechanisms.examples++;
        }
        if (text.includes('glossary') || text.includes('definition') || text.includes('term')) {
          mechanisms.glossaries++;
        }
        if (text.includes('tutorial') || text.includes('guide') || text.includes('walkthrough')) {
          mechanisms.tutorials++;
        }
      });

      return mechanisms;
    });

    const totalMechanismTypes = Object.keys(assistanceMechanisms).length;
    const availableMechanisms = Object.values(assistanceMechanisms).filter(count => count > 0).length;

    const totalChecks = 1;
    let passedChecks = 0;

    // Check if sufficient assistance mechanisms are available
    if (availableMechanisms >= 3) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Sufficient assistance mechanisms available',
        value: `${availableMechanisms}/${totalMechanismTypes} types of assistance mechanisms found`,
        severity: 'info',
      });
    } else {
      issues.push('Insufficient assistance mechanisms available');
      evidence.push({
        type: 'code',
        description: 'More assistance mechanisms needed',
        value: `Only ${availableMechanisms}/${totalMechanismTypes} types found: ${JSON.stringify(assistanceMechanisms)}`,
        severity: 'warning',
      });
      recommendations.push('Add more assistance mechanisms (help links, tooltips, instructions, examples, etc.)');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  // Analyze help mechanisms on the page
  const helpAnalysis = await page.evaluate(() => {
    const helpElements: Array<{
      selector: string;
        element: string;
        hasHelp: boolean;
        helpType: string;
        fieldCount: number;
        complexFields: number;
      }> = [];

      // Check for general help elements
      const helpSelectors = [
        'a[href*="help"]',
        'button[aria-label*="help"]',
        '[role="button"][aria-label*="help"]',
        '.help',
        '.help-button',
        '.help-link',
        '.help-icon',
        '.tooltip',
        '.popover',
        '[data-toggle="tooltip"]',
        '[data-toggle="popover"]',
        '[title]',
        '.question-mark',
        '.info-icon',
        '[aria-describedby]',
      ];

      helpSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          const isAccessible = checkAccessibility(element);
          const hasKeyboardAccess = checkKeyboardAccess(element);
          const hasAriaLabel = element.hasAttribute('aria-label') || element.hasAttribute('aria-labelledby');
          const helpText = getHelpText(element);
          const context = getContext(element);
          const placement = getPlacement(element);

          helpElements.push({
            type: getHelpType(element, selector),
            selector: generateSelector(element, index),
            element: element.tagName.toLowerCase(),
            isAccessible,
            hasKeyboardAccess,
            hasAriaLabel,
            helpText,
            context,
            placement,
          });
        });
      });

      // Check forms for help availability
      const forms = document.querySelectorAll('form');
      forms.forEach((form, index) => {
        const formSelector = generateSelector(form, index);
        const fields = form.querySelectorAll('input, textarea, select');
        const complexFields = form.querySelectorAll('input[type="password"], input[type="email"], input[pattern], textarea, select[multiple]');
        
        // Check if form has help
        const hasGeneralHelp = form.querySelector('.help, .help-text, .instructions, [role="help"]') !== null;
        const hasFieldHelp = Array.from(fields).some(field => hasFieldLevelHelp(field));
        const hasTooltips = form.querySelector('[title], [data-toggle="tooltip"]') !== null;
        const hasAriaDescriptions = Array.from(fields).some(field => field.hasAttribute('aria-describedby'));

        let helpType = 'none';
        if (hasGeneralHelp) helpType = 'general';
        if (hasFieldHelp) helpType = helpType === 'general' ? 'comprehensive' : 'field-level';
        if (hasTooltips) helpType = helpType === 'none' ? 'tooltips' : helpType;
        if (hasAriaDescriptions) helpType = helpType === 'none' ? 'aria-descriptions' : helpType;

        formsNeedingHelp.push({
          selector: formSelector,
          element: 'form',
          hasHelp: helpType !== 'none',
          helpType,
          fieldCount: fields.length,
          complexFields: complexFields.length,
        });
      });

      return {
        helpElements,
        formsNeedingHelp,
        totalForms: forms.length,
        totalHelpElements: helpElements.length,
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function checkAccessibility(element: Element): boolean {
        const computedStyle = window.getComputedStyle(element);
        return computedStyle.display !== 'none' && 
               computedStyle.visibility !== 'hidden' && 
               !element.hasAttribute('aria-hidden') &&
               element.getAttribute('aria-hidden') !== 'true';
      }

      function checkKeyboardAccess(element: Element): boolean {
        const tagName = element.tagName.toLowerCase();
        const tabIndex = element.getAttribute('tabindex');
        
        return tagName === 'a' && element.hasAttribute('href') ||
               tagName === 'button' ||
               tagName === 'input' ||
               tagName === 'textarea' ||
               tagName === 'select' ||
               element.hasAttribute('role') && ['button', 'link', 'menuitem'].includes(element.getAttribute('role') || '') ||
               tabIndex !== null && tabIndex !== '-1';
      }

      function getHelpText(element: Element): string {
        return element.getAttribute('aria-label') ||
               element.getAttribute('title') ||
               element.textContent?.trim().substring(0, 100) ||
               '';
      }

      function getContext(element: Element): string {
        const parent = element.closest('form, fieldset, .form-group, .field, .input-group');
        if (parent) {
          const label = parent.querySelector('label, legend, .label');
          return label?.textContent?.trim().substring(0, 50) || 'form context';
        }
        return 'general';
      }

      function getPlacement(element: Element): string {
        const rect = element.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        
        if (rect.top < viewportHeight * 0.3) return 'top';
        if (rect.top > viewportHeight * 0.7) return 'bottom';
        if (rect.left < viewportWidth * 0.3) return 'left';
        if (rect.left > viewportWidth * 0.7) return 'right';
        return 'center';
      }

      function getHelpType(element: Element, selector: string): string {
        if (selector.includes('tooltip')) return 'tooltip';
        if (selector.includes('popover')) return 'popover';
        if (element.tagName.toLowerCase() === 'a') return 'help-link';
        if (element.tagName.toLowerCase() === 'button') return 'help-button';
        if (element.hasAttribute('title')) return 'title-attribute';
        if (element.hasAttribute('aria-describedby')) return 'aria-description';
        return 'help-element';
      }

      function hasFieldLevelHelp(field: Element): boolean {
        const fieldId = field.getAttribute('id');
        if (fieldId) {
          // Check for aria-describedby
          const describedBy = field.getAttribute('aria-describedby');
          if (describedBy) {
            const helpElement = document.getElementById(describedBy);
            if (helpElement) return true;
          }
        }

        // Check for nearby help elements
        const parent = field.closest('.form-group, .field, .input-group, fieldset');
        if (parent) {
          return parent.querySelector('.help, .help-text, .hint, .description, [role="help"]') !== null;
        }

        return false;
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = helpAnalysis.helpElements.length;

    // Analyze help availability
    const formsWithoutHelp = helpAnalysis.formsNeedingHelp.filter(form => !form.hasHelp && form.fieldCount > 2);
    const inaccessibleHelp = helpAnalysis.helpElements.filter(help => !help.isAccessible || !help.hasKeyboardAccess);

    if (formsWithoutHelp.length > 0) {
      score -= Math.min(40, formsWithoutHelp.length * 20);
      issues.push(`${formsWithoutHelp.length} forms lack contextual help`);
    }

    if (inaccessibleHelp.length > 0) {
      score -= Math.min(30, inaccessibleHelp.length * 10);
      issues.push(`${inaccessibleHelp.length} help elements are not accessible`);
    }

    if (elementCount > 0) {
      const accessibleHelp = helpAnalysis.helpElements.filter(help => help.isAccessible && help.hasKeyboardAccess);
      
      evidence.push({
        type: 'interaction',
        description: 'Help elements analysis',
        value: `Found ${elementCount} help elements, ${accessibleHelp.length} are fully accessible`,
        elementCount,
        affectedSelectors: helpAnalysis.helpElements.map(help => help.selector),
        severity: inaccessibleHelp.length > 0 ? 'warning' : 'info',
        fixExample: {
          before: '<input type="password" placeholder="Password">',
          after: '<label for="password">Password</label><input type="password" id="password" aria-describedby="pwd-help"><div id="pwd-help" role="help">Must be 8+ characters with numbers and symbols</div>',
          description: 'Provide accessible contextual help for complex form fields',
          codeExample: `
<!-- Before: No help provided -->
<form>
  <input type="email" placeholder="Email">
  <input type="password" placeholder="Password">
  <input type="tel" placeholder="Phone">
</form>

<!-- After: Comprehensive help system -->
<form>
  <div class="field">
    <label for="email">Email Address</label>
    <input type="email" id="email" aria-describedby="email-help">
    <div id="email-help" role="help">We'll use this to send you important updates</div>
  </div>
  
  <div class="field">
    <label for="password">Password 
      <button type="button" aria-label="Password requirements help" data-toggle="tooltip" 
              title="Must be 8+ characters with uppercase, lowercase, numbers, and symbols">
        ?
      </button>
    </label>
    <input type="password" id="password" aria-describedby="pwd-help">
    <div id="pwd-help" role="help">
      <ul>
        <li>At least 8 characters long</li>
        <li>Include uppercase and lowercase letters</li>
        <li>Include at least one number</li>
        <li>Include at least one symbol</li>
      </ul>
    </div>
  </div>
  
  <div class="field">
    <label for="phone">Phone Number</label>
    <input type="tel" id="phone" aria-describedby="phone-help">
    <div id="phone-help" role="help">Format: (*************</div>
  </div>
  
  <div class="form-help">
    <h3>Need Help?</h3>
    <p><a href="/contact">Contact Support</a> | <a href="/faq">View FAQ</a></p>
  </div>
</form>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/help.html',
            'https://www.w3.org/WAI/WCAG21/Techniques/general/G184',
            'https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-describedby'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: helpAnalysis.totalHelpElements,
          checkSpecificData: {
            totalHelpElements: elementCount,
            accessibleHelp: accessibleHelp.length,
            inaccessibleHelp: inaccessibleHelp.length,
            totalForms: helpAnalysis.totalForms,
            formsWithHelp: helpAnalysis.formsNeedingHelp.filter(f => f.hasHelp).length,
            formsWithoutHelp: formsWithoutHelp.length,
            helpTypes: [...new Set(helpAnalysis.helpElements.map(h => h.type))],
          },
        },
      });

      // Add specific examples for help elements
      helpAnalysis.helpElements.slice(0, 5).forEach(helpElement => {
        evidence.push({
          type: 'interaction',
          description: `Help element: ${helpElement.type}`,
          value: `Context: ${helpElement.context} | Text: "${helpElement.helpText}"`,
          selector: helpElement.selector,
          severity: helpElement.isAccessible && helpElement.hasKeyboardAccess ? 'info' : 'warning',
          metadata: {
            checkSpecificData: {
              type: helpElement.type,
              isAccessible: helpElement.isAccessible,
              hasKeyboardAccess: helpElement.hasKeyboardAccess,
              hasAriaLabel: helpElement.hasAriaLabel,
              placement: helpElement.placement,
            },
          },
        });
      });

      // Add examples for forms without help
      formsWithoutHelp.forEach(form => {
        evidence.push({
          type: 'interaction',
          description: 'Form without contextual help',
          value: `Form with ${form.fieldCount} fields (${form.complexFields} complex) lacks help`,
          selector: form.selector,
          severity: 'warning',
          metadata: {
            checkSpecificData: {
              fieldCount: form.fieldCount,
              complexFields: form.complexFields,
              helpType: form.helpType,
            },
          },
        });
      });

      recommendations.push('Provide contextual help for complex forms and input fields');
      recommendations.push('Ensure help elements are keyboard accessible');
      recommendations.push('Use aria-describedby to associate help text with form fields');
      recommendations.push('Consider multiple help formats (tooltips, inline text, help pages)');
      
      if (formsWithoutHelp.length > 0) {
        recommendations.push('Add help text for forms with multiple or complex fields');
      }
      
      if (inaccessibleHelp.length > 0) {
        recommendations.push('Make help elements accessible to keyboard and screen reader users');
      }
    } else {
      // No help elements found
      if (helpAnalysis.totalForms > 0) {
        score -= 30;
        issues.push('No help elements found despite presence of forms');
        
        evidence.push({
          type: 'interaction',
          description: 'Missing help system',
          value: `Found ${helpAnalysis.totalForms} forms but no help elements`,
          severity: 'warning',
          metadata: {
            scanDuration,
            elementsAnalyzed: 0,
            checkSpecificData: {
              totalForms: helpAnalysis.totalForms,
              noHelpElements: true,
            },
          },
        });

        recommendations.push('CRITICAL: Add contextual help for forms and complex interactions');
        recommendations.push('Consider implementing tooltips, help text, or help pages');
      } else {
        // No forms, so help may not be needed
        evidence.push({
          type: 'info',
          description: 'No forms requiring help detected',
          value: 'Page does not contain forms or complex interactions that require help',
          severity: 'info',
          metadata: {
            scanDuration,
            elementsAnalyzed: 0,
            checkSpecificData: {
              noFormsFound: true,
            },
          },
        });
      }
    }

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
